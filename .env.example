# Database
MONGODB_URI=mongodb://localhost:27017/sabu-raijua

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-refresh-secret-here
JWT_EXPIRES_IN=24h

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>

# Admin Passwords (Change after first login!)
SUPER_ADMIN_PASSWORD=SuperAdmin123!@#
ADMIN_PASSWORD=Admin123!@#
EDITOR_PASSWORD=Editor123!@#

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
ENCRYPTION_KEY=your-32-character-encryption-key-here
SESSION_SECRET=your-session-secret-key-here
SEARCH_SALT=your-search-salt-for-hashing

# Cloudflare Turnstile (for admin login and contact form protection)
# For localhost development testing, use these dummy keys:
# Always passes (visible): 1x00000000000000000000AA / 1x0000000000000000000000000000000AA
# Always blocks (visible): 2x00000000000000000000AB / 2x0000000000000000000000000000000AA
# Always passes (invisible): 1x00000000000000000000BB / 1x0000000000000000000000000000000AA
# Interactive challenge: 3x00000000000000000000FF / 1x0000000000000000000000000000000AA
NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY=1x00000000000000000000AA
CLOUDFLARE_TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
