{"name": "company-profile", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed:admin": "bun scripts/seed-admin.js", "seed:admin:list": "bun scripts/seed-admin.js list", "seed:admin:reset": "bun scripts/seed-admin.js reset", "seed:kecamatan": "bun scripts/seed-kecamatan.js", "seed:kecamatan:update": "bun scripts/seed-kecamatan.js update"}, "dependencies": {"@aws-sdk/client-s3": "^3.830.0", "@aws-sdk/s3-request-presigner": "^3.830.0", "@marsidev/react-turnstile": "^1.1.0", "@next/env": "^15.3.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.0.5", "@types/jsonwebtoken": "^9.0.10", "@types/leaflet": "^1.9.18", "@types/nodemailer": "^6.4.17", "@types/validator": "^13.15.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "i18next": "^25.2.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.516.0", "maplibre-gl": "^5.6.0", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "multer": "^2.0.1", "next": "15.3.3", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.3", "react-leaflet": "^5.0.0", "recharts": "^2.15.4", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1", "validator": "^13.15.15", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/google.analytics": "^0.0.46", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "form-data": "^4.0.3", "node-fetch": "^3.3.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5"}}